{"compilerOptions": {"checkJs": true, "allowJs": true, "jsx": "react", "baseUrl": ".", "paths": {"/*": ["./*"], "meteor/hot-module-replacement": ["/Users/<USER>/.meteor/packages/hot-module-replacement/0.5.4/web.browser/hot-api.js", "/Users/<USER>/.meteor/packages/hot-module-replacement/0.5.4/web.browser/client.js", "/Users/<USER>/.meteor/packages/hot-module-replacement/0.5.4/web.browser/server.js"], "meteor/mongo": ["/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/local_collection_driver.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/collection.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/connection_options.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/doc_fetcher.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/mongo_driver.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/mongo_utils.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/observe_multiplex.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/oplog_observe_driver.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/oplog_tailing.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/oplog_v2_converter.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/polling_observe_driver.js", "/Users/<USER>/.meteor/packages/mongo/2.0.0/web.browser/remote_collection_driver.js"], "meteor/ecmascript": ["/Users/<USER>/.meteor/packages/ecmascript/0.16.9/web.browser/ecmascript.js"], "meteor/react-meteor-data": ["/Users/<USER>/.meteor/packages/react-meteor-data/2.0.1/web.browser/index.js", "/Users/<USER>/.meteor/packages/react-meteor-data/2.0.1/web.browser/useTracker.js"]}}, "typeAcquisition": {"include": ["meteor"]}, "exclude": ["node_modules", ".meteor"]}
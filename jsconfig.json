{"compilerOptions": {"checkJs": true, "allowJs": true, "jsx": "react", "baseUrl": ".", "paths": {"/*": ["./*"], "meteor/hot-module-replacement": ["/Users/<USER>/.meteor/packages/hot-module-replacement/0.5.4/web.browser/hot-api.js", "/Users/<USER>/.meteor/packages/hot-module-replacement/0.5.4/web.browser/client.js", "/Users/<USER>/.meteor/packages/hot-module-replacement/0.5.4/web.browser/server.js"]}}, "typeAcquisition": {"include": ["meteor"]}, "exclude": ["node_modules", ".meteor"]}
{"name": "quave-code-challenge", "private": true, "scripts": {"start": "meteor run --exclude-archs web.browser.legacy,web.cordova", "quave-eslint": "eslint .", "quave-prettier": "prettier --write \"**/*.js\"", "quave-check": "npm run quave-eslint && npm run quave-prettier"}, "dependencies": {"@babel/runtime": "^7.24.7", "meteor-node-stubs": "^1.2.9", "react": "^18.3.1", "react-dom": "^18.3.1"}, "meteor": {"mainModule": {"client": "client/main.js", "server": "server/main.js"}}, "devDependencies": {"@quave/eslint-config-quave": "^3.0.0-beta.0", "@types/meteor": "^2.9.8", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "lefthook": "^1.6.18", "postcss": "^8.4.38", "tailwindcss": "^3.4.4"}, "eslintConfig": {"extends": ["@quave/quave"]}}
import { Meteor } from 'meteor/meteor';
import { loadInitialData } from '../infra/initial-data';
import { Communities } from '../communities/communities';
import { People } from '../people/people';

Meteor.startup(async () => {
  // DON'T CHANGE THE NEXT LINE
  await loadInitialData();

  // Publications
  Meteor.publish('communities', () => Communities.find({}));

  Meteor.publish('people', function publishPeople(communityId) {
    if (!communityId) {
      return this.ready();
    }
    return People.find({ communityId });
  });
});

// Methods
Meteor.methods({
  async 'people.checkIn'(personId) {
    const person = await People.findOneAsync(personId);
    if (!person) {
      throw new Meteor.Error('person-not-found', 'Person not found');
    }

    const result = await People.updateAsync(personId, {
      $set: {
        checkInDate: new Date(),
        checkOutDate: null,
      },
    });

    return  result;
  },

  async 'people.checkOut'(personId) {
    const person = await People.findOneAsync(personId);
    if (!person) {
      throw new Meteor.Error('person-not-found', 'Person not found');
    }

    const result = await People.updateAsync(personId, {
      $set: {
        checkOutDate: new Date(),
      },
    });

    return result;
  },
});

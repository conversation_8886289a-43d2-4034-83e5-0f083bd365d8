import React, { useState } from 'react';
import { EventSelector } from './components/EventSelector';
import { Summary } from './components/Summary';
import { PeopleList } from './components/PeopleList';

export const App = () => {
  const [selectedCommunityId, setSelectedCommunityId] = useState('');

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <h1 className="mb-8 text-center text-3xl font-bold text-gray-900">
          Event Check-in System
        </h1>

        <div className="space-y-6">
          <EventSelector
            selectedCommunityId={selectedCommunityId}
            onCommunityChange={setSelectedCommunityId}
          />

          {selectedCommunityId && (
            <>
              <Summary communityId={selectedCommunityId} />
              <PeopleList communityId={selectedCommunityId} />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

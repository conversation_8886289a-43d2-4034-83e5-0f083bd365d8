import React from 'react';
import { Meteor } from 'meteor/meteor';
import { useTracker } from 'meteor/react-meteor-data';
import { Communities } from '../../communities/communities';

export const EventSelector = ({ selectedCommunityId, onCommunityChange }) => {
  const { communities, isLoading } = useTracker(() => {
    const handle = Meteor.subscribe('communities');
    return {
      communities: Communities.find().fetch(),
      isLoading: !handle.ready(),
    };
  }, []);

  if (isLoading) {
    return (
      <div className="rounded-lg bg-white p-6 shadow">
        <div className="animate-pulse">
          <div className="mb-2 h-4 w-1/4 rounded bg-gray-200" />
          <div className="h-10 rounded bg-gray-200" />
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-white p-6 shadow">
      <label
        htmlFor="event-selector"
        className="mb-2 block text-sm font-medium text-gray-700"
      >
        Select an Event
      </label>
      <select
        id="event-selector"
        value={selectedCommunityId}
        onChange={(e) => onCommunityChange(e.target.value)}
        className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
      >
        <option value="">Select an event</option>
        {communities.map((community) => (
          <option key={community._id} value={community._id}>
            {community.name}
          </option>
        ))}
      </select>
    </div>
  );
};

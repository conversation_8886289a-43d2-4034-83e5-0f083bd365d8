import React from 'react';
import { Meteor } from 'meteor/meteor';
import { useTracker } from 'meteor/react-meteor-data';
import { People } from '../../people/people';
import { PersonCard } from './PersonCard';

export const PeopleList = ({ communityId }) => {
  const { people, isLoading } = useTracker(() => {
    const handle = Meteor.subscribe('people', communityId);
    return {
      people: People.find(
        { communityId },
        { sort: { lastName: 1, firstName: 1 } }
      ).fetch(),
      isLoading: !handle.ready(),
    };
  }, [communityId]);

  if (isLoading) {
    return (
      <div className="rounded-lg bg-white p-6 shadow">
        <h2 className="mb-4 text-lg font-semibold text-gray-900">
          Event Attendees
        </h2>
        <div className="space-y-4">
          {[...Array(6)].map(() => (
            <div className="animate-pulse">
              <div className="h-32 rounded-lg bg-gray-200" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (people.length === 0) {
    return (
      <div className="rounded-lg bg-white p-6 shadow">
        <h2 className="mb-4 text-lg font-semibold text-gray-900">
          Event Attendees
        </h2>
        <div className="py-8 text-center">
          <p className="text-gray-500">No people registered for this event.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-white p-6 shadow">
      <h2 className="mb-4 text-lg font-semibold text-gray-900">
        Event Attendees ({people.length})
      </h2>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {people.map((person) => (
          <PersonCard key={person._id} person={person} />
        ))}
      </div>
    </div>
  );
};

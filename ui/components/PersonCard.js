import React from 'react';
import { Meteor } from 'meteor/meteor';

export const PersonCard = ({ person }) => {
  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    }).format(new Date(date));
  };

  const handleCheckIn = () => {
    Meteor.call('people.checkIn', person._id, (error) => {
      if (error) {
        console.error('Check-in failed:', error);
      }
    });
  };

  const handleCheckOut = () => {
    Meteor.call('people.checkOut', person._id, (error) => {
      if (error) {
        console.error('Check-out failed:', error);
      }
    });
  };

  const isCheckedIn = person.checkInDate && !person.checkOutDate;

  const canCheckOut =
    isCheckedIn &&
    person.checkInDate &&
    Date.now() - new Date(person.checkInDate).getTime() > 5000; // 5 seconds

  const fullName = `${person.firstName} ${person.lastName}`;

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow">
      <div className="mb-4 flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{fullName}</h3>
          {person.companyName && (
            <p className="text-sm text-gray-600">{person.companyName}</p>
          )}
          {person.title && (
            <p className="text-sm text-gray-500">{person.title}</p>
          )}
        </div>
        <div className="ml-4">
          {isCheckedIn ? (
            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
              Checked In
            </span>
          ) : (
            <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
              Not Checked In
            </span>
          )}
        </div>
      </div>

      <div className="mb-4 grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="font-medium text-gray-700">Check-in:</span>
          <p className="text-gray-600">{formatDate(person.checkInDate)}</p>
        </div>
        <div>
          <span className="font-medium text-gray-700">Check-out:</span>
          <p className="text-gray-600">{formatDate(person.checkOutDate)}</p>
        </div>
      </div>

      <div className="flex space-x-3">
        {!isCheckedIn ? (
          <button
            onClick={handleCheckIn}
            className="flex-1 rounded-md bg-blue-600 px-4 py-2 font-medium text-white transition duration-150 ease-in-out hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Check-in {fullName}
          </button>
        ) : (
          (canCheckOut) && (
            <button
              onClick={handleCheckOut}
              className="flex-1 rounded-md bg-red-600 px-4 py-2 font-medium text-white transition duration-150 ease-in-out hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Check-out {fullName}
            </button>
          )
        )}
      </div>
    </div>
  );
};

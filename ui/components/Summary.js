import React from 'react';
import { Meteor } from 'meteor/meteor';
import { useTracker } from 'meteor/react-meteor-data';
import { People } from '../../people/people';

export const Summary = ({ communityId }) => {
  const { people, isLoading } = useTracker(() => {
    const handle = Meteor.subscribe('people', communityId);
    return {
      people: People.find({ communityId }).fetch(),
      isLoading: !handle.ready(),
    };
  }, [communityId]);

  if (isLoading) {
    return (
      <div className="rounded-lg bg-white p-6 shadow">
        <div className="animate-pulse space-y-3">
          <div className="h-4 w-3/4 rounded bg-gray-200" />
          <div className="h-4 w-full rounded bg-gray-200" />
          <div className="h-4 w-2/3 rounded bg-gray-200" />
        </div>
      </div>
    );
  }

  // Calculate statistics
  const checkedInPeople = people.filter(
    (person) => person.checkInDate && !person.checkOutDate
  );

  const notCheckedInPeople = people.filter(
    (person) => !person.checkInDate || person.checkOutDate
  );

  // Group checked-in people by company
  const companyCounts = checkedInPeople.reduce((acc, person) => {
    const company = person.companyName || 'No Company';
    acc[company] = (acc[company] || 0) + 1;
    return acc;
  }, {});

  const companyCountsText = Object.entries(companyCounts)
    .map(([company, count]) => `${company} (${count})`)
    .join(', ');

  return (
    <div className="rounded-lg bg-white p-6 shadow">
      <h2 className="mb-4 text-lg font-semibold text-gray-900">
        Event Summary
      </h2>
      <div className="space-y-2 text-sm">
        <div className="flex items-center">
          <span className="mr-2 inline-block h-3 w-3 rounded-full bg-green-500" />
          <span className="text-gray-700">
            People in the event right now:{' '}
            <span className="font-semibold">{checkedInPeople.length}</span>
          </span>
        </div>

        {companyCountsText && (
          <div className="flex items-start">
            <span className="mr-2 mt-1 inline-block h-3 w-3 rounded-full bg-blue-500" />
            <span className="text-gray-700">
              People by company in the event right now:{' '}
              <span className="font-semibold">{companyCountsText}</span>
            </span>
          </div>
        )}

        <div className="flex items-center">
          <span className="mr-2 inline-block h-3 w-3 rounded-full bg-gray-400" />
          <span className="text-gray-700">
            People not checked in:{' '}
            <span className="font-semibold">{notCheckedInPeople.length}</span>
          </span>
        </div>
      </div>
    </div>
  );
};
